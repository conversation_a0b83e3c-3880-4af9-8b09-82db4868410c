{"name": "test-deploy", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "node .next/standalone/server.js", "lint": "next lint", "gcp-build": "npm run build"}, "dependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "next": "15.3.5"}, "devDependencies": {"typescript": "^5", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@tailwindcss/postcss": "^4", "tailwindcss": "^4"}, "engines": {"node": "22.x"}}