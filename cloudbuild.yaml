steps:
  # Build the Docker image
  - name: 'gcr.io/cloud-builders/docker'
    args: [
      'build',
      '-t', 'gcr.io/$PROJECT_ID/${_SERVICE_NAME}:$COMMIT_SHA',
      '-t', 'gcr.io/$PROJECT_ID/${_SERVICE_NAME}:latest',
      '--build-arg', 'NEXT_PUBLIC_MY_ENV_VAR=${_NEXT_PUBLIC_MY_ENV_VAR}',
      '--build-arg', 'SERVER_ENV_VAR=${_SERVER_ENV_VAR}',
      '.'
    ]

  # Push the image to Container Registry
  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/${_SERVICE_NAME}:$COMMIT_SHA']

  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/${_SERVICE_NAME}:latest']

  # Deploy to Cloud Run
  - name: 'gcr.io/cloud-builders/gcloud'
    args: [
      'run', 'deploy', '${_SERVICE_NAME}',
      '--image', 'gcr.io/$PROJECT_ID/${_SERVICE_NAME}:$COMMIT_SHA',
      '--region', '${_REGION}',
      '--platform', 'managed',
      '--allow-unauthenticated',
      '--port', '8080',
      '--memory', '${_MEMORY}',
      '--cpu', '${_CPU}',
      '--min-instances', '${_MIN_INSTANCES}',
      '--max-instances', '${_MAX_INSTANCES}',
      '--set-env-vars', 'NODE_ENV=production',
      '--update-env-vars', 'NEXT_PUBLIC_MY_ENV_VAR=${_NEXT_PUBLIC_MY_ENV_VAR},SERVER_ENV_VAR=${_SERVER_ENV_VAR}'
    ]

# Substitution variables with defaults
substitutions:
  _SERVICE_NAME: 'my-nextjs-app'
  _REGION: 'us-central1'
  _MEMORY: '512Mi'
  _CPU: '1'
  _MIN_INSTANCES: '0'
  _MAX_INSTANCES: '10'
  _NEXT_PUBLIC_MY_ENV_VAR: ''
  _SERVER_ENV_VAR: ''

# Images to be pushed to Container Registry
images:
  - 'gcr.io/$PROJECT_ID/${_SERVICE_NAME}:$COMMIT_SHA'
  - 'gcr.io/$PROJECT_ID/${_SERVICE_NAME}:latest'

options:
  logging: CLOUD_LOGGING_ONLY
