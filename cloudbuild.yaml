steps:
  # Get environment variables from the existing Cloud Run service
  - name: 'gcr.io/cloud-builders/gcloud'
    id: 'get-env-vars'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        # Get existing environment variables from Cloud Run service
        gcloud run services describe ${_SERVICE_NAME} --region=${_REGION} --format="value(spec.template.spec.template.spec.containers[0].env[].name,spec.template.spec.template.spec.containers[0].env[].value)" > /workspace/env_vars.txt || echo "Service not found, using defaults"

        # Extract specific variables we need for build
        if [ -f /workspace/env_vars.txt ]; then
          NEXT_PUBLIC_VAR=$(grep -A1 "NEXT_PUBLIC_MY_ENV_VAR" /workspace/env_vars.txt | tail -1 || echo "")
          SERVER_VAR=$(grep -A1 "SERVER_ENV_VAR" /workspace/env_vars.txt | tail -1 || echo "")
          echo "NEXT_PUBLIC_MY_ENV_VAR=$NEXT_PUBLIC_VAR" > /workspace/build_env.txt
          echo "SERVER_ENV_VAR=$SERVER_VAR" >> /workspace/build_env.txt
        else
          echo "NEXT_PUBLIC_MY_ENV_VAR=" > /workspace/build_env.txt
          echo "SERVER_ENV_VAR=" >> /workspace/build_env.txt
        fi

  # Deploy to Cloud Run using source-based deployment with build env vars
  - name: 'gcr.io/cloud-builders/gcloud'
    args: [
      'run', 'deploy', '${_SERVICE_NAME}',
      '--source', '.',
      '--region', '${_REGION}',
      '--platform', 'managed',
      '--port', '8080',
      '--memory', '${_MEMORY}',
      '--cpu', '${_CPU}',
      '--min-instances', '${_MIN_INSTANCES}',
      '--max-instances', '${_MAX_INSTANCES}',
      '--timeout', '3600',
      '--set-build-env-vars-file', '/workspace/build_env.txt'
    ]

# Substitution variables with defaults
# These can be overridden in the Cloud Build trigger settings
substitutions:
  _SERVICE_NAME: 'nextjs-cloud-run-deploy'
  _REGION: 'europe-west1'
  _MEMORY: '1Gi'
  _CPU: '1'
  _MIN_INSTANCES: '0'
  _MAX_INSTANCES: '10'

# Build timeout (important for Next.js builds)
timeout: '1200s'



options:
  logging: CLOUD_LOGGING_ONLY
