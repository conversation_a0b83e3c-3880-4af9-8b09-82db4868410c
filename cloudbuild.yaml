steps:
  # Step 1: Get environment variables from existing Cloud Run service
  - name: 'gcr.io/cloud-builders/gcloud'
    id: 'get-env-vars'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        echo "Getting environment variables from Cloud Run service..."

        # Get environment variables from the existing service in YAML format
        gcloud run services describe ${_SERVICE_NAME} \
          --region=${_REGION} \
          --format="get(spec.template.spec.template.spec.containers[0].env)" > /workspace/env_raw.txt 2>/dev/null || echo "[]" > /workspace/env_raw.txt

        echo "Raw environment data:"
        cat /workspace/env_raw.txt

        # Extract environment variables using a more robust method
        gcloud run services describe ${_SERVICE_NAME} \
          --region=${_REGION} \
          --format="value(spec.template.spec.template.spec.containers[0].env[].name,spec.template.spec.template.spec.containers[0].env[].value)" > /workspace/env_pairs.txt 2>/dev/null || touch /workspace/env_pairs.txt

        echo "Environment pairs:"
        cat /workspace/env_pairs.txt

        # Convert to build env format (name=value)
        > /workspace/build_env.txt
        while IFS= read -r line; do
          if [[ -n "$line" ]]; then
            if [[ "$line" =~ ^[A-Z_][A-Z0-9_]*$ ]]; then
              # This is a variable name, read the next line for value
              var_name="$line"
              read -r var_value
              echo "${var_name}=${var_value}" >> /workspace/build_env.txt
            fi
          fi
        done < /workspace/env_pairs.txt

        echo "Final build environment file:"
        cat /workspace/build_env.txt

  # Step 2: Deploy to Cloud Run using source-based deployment with extracted env vars
  - name: 'gcr.io/cloud-builders/gcloud'
    args: [
      'run', 'deploy', '${_SERVICE_NAME}',
      '--source', '.',
      '--region', '${_REGION}',
      '--platform', 'managed',
      '--port', '8080',
      '--memory', '${_MEMORY}',
      '--cpu', '${_CPU}',
      '--min-instances', '${_MIN_INSTANCES}',
      '--max-instances', '${_MAX_INSTANCES}',
      '--timeout', '3600',
      '--set-build-env-vars-file', '/workspace/build_env.txt'
    ]

# Substitution variables with defaults
# These can be overridden in the Cloud Build trigger settings
substitutions:
  _SERVICE_NAME: 'nextjs-cloud-run-deploy'
  _REGION: 'europe-west1'
  _MEMORY: '1Gi'
  _CPU: '1'
  _MIN_INSTANCES: '0'
  _MAX_INSTANCES: '10'

# Build timeout (important for Next.js builds)
timeout: '1200s'



options:
  logging: CLOUD_LOGGING_ONLY
