steps:
  # Deploy to Cloud Run using source-based deployment
  # Cloud Run will handle the build process automatically
  - name: 'gcr.io/cloud-builders/gcloud'
    args: [
      'run', 'deploy', '${_SERVICE_NAME}',
      '--source', '.',
      '--region', '${_REGION}',
      '--platform', 'managed',
      '--port', '8080',
      '--memory', '${_MEMORY}',
      '--cpu', '${_CPU}',
      '--min-instances', '${_MIN_INSTANCES}',
      '--max-instances', '${_MAX_INSTANCES}',
      '--timeout', '3600',
      '--set-env-vars', 'NODE_ENV=production',
      '--update-env-vars', 'NEXT_PUBLIC_MY_ENV_VAR=${_NEXT_PUBLIC_MY_ENV_VAR},SERVER_ENV_VAR=${_SERVER_ENV_VAR}'
    ]
    env:
      # Build-time environment variables for Next.js build process
      - 'NODE_ENV=production'
      - 'NEXT_PUBLIC_MY_ENV_VAR=${_NEXT_PUBLIC_MY_ENV_VAR}'
      - 'SERVER_ENV_VAR=${_SERVER_ENV_VAR}'

# Substitution variables with defaults
# These can be overridden in the Cloud Build trigger settings
substitutions:
  _SERVICE_NAME: 'test-deploy'
  _REGION: 'europe-west1'
  _MEMORY: '1Gi'
  _CPU: '1'
  _MIN_INSTANCES: '0'
  _MAX_INSTANCES: '10'
  _NEXT_PUBLIC_MY_ENV_VAR: ''
  _SERVER_ENV_VAR: ''

# Build timeout (important for Next.js builds)
timeout: '1200s'



options:
  logging: CLOUD_LOGGING_ONLY
